{"numTotalTestSuites": 2, "numPassedTestSuites": 2, "numFailedTestSuites": 0, "numPendingTestSuites": 0, "numTotalTests": 13, "numPassedTests": 13, "numFailedTests": 0, "numPendingTests": 0, "numTodoTests": 0, "snapshot": {"added": 0, "failure": false, "filesAdded": 0, "filesRemoved": 0, "filesRemovedList": [], "filesUnmatched": 0, "filesUpdated": 0, "matched": 0, "total": 0, "unchecked": 0, "uncheckedKeysByFile": [], "unmatched": 0, "updated": 0, "didUpdate": false}, "startTime": 1752846318721, "success": true, "testResults": [{"assertionResults": [{"ancestorTitles": ["ReferralLinkCard"], "fullName": "ReferralLinkCard should render referral link card with user data", "status": "passed", "title": "should render referral link card with user data", "duration": 135.70061699999997, "failureMessages": [], "location": {"line": 64, "column": 3}, "meta": {}}, {"ancestorTitles": ["ReferralLinkCard"], "fullName": "ReferralLinkCard should generate referral link on mount", "status": "passed", "title": "should generate referral link on mount", "duration": 30.*************, "failureMessages": [], "location": {"line": 73, "column": 3}, "meta": {}}, {"ancestorTitles": ["ReferralLinkCard"], "fullName": "ReferralLinkCard should handle invalid referral link generation", "status": "passed", "title": "should handle invalid referral link generation", "duration": 5.517297999999755, "failureMessages": [], "location": {"line": 80, "column": 3}, "meta": {}}, {"ancestorTitles": ["ReferralLinkCard"], "fullName": "ReferralLinkCard should handle invalid referral link validation", "status": "passed", "title": "should handle invalid referral link validation", "duration": 8.62087799999972, "failureMessages": [], "location": {"line": 88, "column": 3}, "meta": {}}, {"ancestorTitles": ["ReferralLinkCard"], "fullName": "ReferralLinkCard should copy referral link when copy button is clicked", "status": "passed", "title": "should copy referral link when copy button is clicked", "duration": 217.11470999999983, "failureMessages": [], "location": {"line": 96, "column": 3}, "meta": {}}, {"ancestorTitles": ["ReferralLinkCard"], "fullName": "ReferralLinkCard should copy referral code when copy code button is clicked", "status": "passed", "title": "should copy referral code when copy code button is clicked", "duration": 121.02576399999998, "failureMessages": [], "location": {"line": 112, "column": 3}, "meta": {}}, {"ancestorTitles": ["ReferralLinkCard"], "fullName": "ReferralLinkCard should share referral link when share button is clicked", "status": "passed", "title": "should share referral link when share button is clicked", "duration": 74.60884900000019, "failureMessages": [], "location": {"line": 124, "column": 3}, "meta": {}}, {"ancestorTitles": ["ReferralLinkCard"], "fullName": "ReferralLinkCard should copy referral stats when share stats button is clicked", "status": "passed", "title": "should copy referral stats when share stats button is clicked", "duration": 42.38456200000019, "failureMessages": [], "location": {"line": 135, "column": 3}, "meta": {}}, {"ancestorTitles": ["ReferralLinkCard"], "fullName": "ReferralLinkCard should handle copy failures gracefully", "status": "passed", "title": "should handle copy failures gracefully", "duration": 69.83831200000031, "failureMessages": [], "location": {"line": 155, "column": 3}, "meta": {}}, {"ancestorTitles": ["ReferralLinkCard"], "fullName": "ReferralLinkCard should not perform actions when referral link is null", "status": "passed", "title": "should not perform actions when referral link is null", "duration": 4.507654000000002, "failureMessages": [], "location": {"line": 173, "column": 3}, "meta": {}}, {"ancestorTitles": ["ReferralLinkCard"], "fullName": "ReferralLinkCard should display how it works section", "status": "passed", "title": "should display how it works section", "duration": 27.04262899999958, "failureMessages": [], "location": {"line": 189, "column": 3}, "meta": {}}, {"ancestorTitles": ["ReferralLinkCard"], "fullName": "ReferralLinkCard should show QR code coming soon message", "status": "passed", "title": "should show QR code coming soon message", "duration": 76.0554519999996, "failureMessages": [], "location": {"line": 199, "column": 3}, "meta": {}}, {"ancestorTitles": ["ReferralLinkCard"], "fullName": "ReferralLinkCard should select text when input is clicked", "status": "passed", "title": "should select text when input is clicked", "duration": 17.71011500000077, "failureMessages": [], "location": {"line": 208, "column": 3}, "meta": {}}], "startTime": 1752846323029, "endTime": 1752846323859.7102, "status": "passed", "message": "", "name": "/home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/src/components/referral/__tests__/ReferralLinkCard.test.tsx"}]}