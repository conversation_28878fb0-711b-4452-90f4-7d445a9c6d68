// Test script to verify M-Pesa API connection from frontend perspective
import axios from 'axios';

const API_BASE_URL = 'http://localhost:3001/api';

// Test data
const testPaymentData = {
  walletAddress: '******************************************',
  packageId: 1,
  phoneNumber: '254712345678',
  amount: 100,
  referrerAddress: '******************************************'
};

async function testMpesaConnection() {
  console.log('🧪 Testing M-Pesa API Connection...\n');

  try {
    // Test 1: Health check
    console.log('1️⃣ Testing health endpoint...');
    const healthResponse = await axios.get(`${API_BASE_URL.replace('/api', '')}/health`);
    console.log('✅ Health check passed:', healthResponse.data);
    console.log('');

    // Test 2: Initiate payment
    console.log('2️⃣ Testing payment initiation...');
    const paymentResponse = await axios.post(`${API_BASE_URL}/mpesa/initiate-payment`, testPaymentData);
    console.log('✅ Payment initiation passed:', paymentResponse.data);
    console.log('');

    // Test 3: Query payment status
    console.log('3️⃣ Testing payment status query...');
    const statusResponse = await axios.get(`${API_BASE_URL}/mpesa/status/${paymentResponse.data.checkoutRequestId}`);
    console.log('✅ Payment status query passed:', statusResponse.data);
    console.log('');

    // Test 4: Get transaction history
    console.log('4️⃣ Testing transaction history...');
    const historyResponse = await axios.get(`${API_BASE_URL}/mpesa/transactions/${testPaymentData.walletAddress}?limit=10&offset=0`);
    console.log('✅ Transaction history passed:', historyResponse.data);
    console.log('');

    // Test 5: Test CORS headers
    console.log('5️⃣ Testing CORS headers...');
    const corsResponse = await axios.options(`${API_BASE_URL}/mpesa/initiate-payment`);
    console.log('✅ CORS test passed. Status:', corsResponse.status);
    console.log('');

    console.log('🎉 All M-Pesa API tests passed successfully!');
    console.log('✅ Frontend can successfully connect to backend M-Pesa API');
    console.log('✅ The ERR_CONNECTION_REFUSED error should now be resolved');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
    if (error.code === 'ECONNREFUSED') {
      console.error('🔴 Connection refused - make sure the backend server is running on port 3001');
    }
  }
}

// Run the test
testMpesaConnection();
