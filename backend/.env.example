# Server Configuration
PORT=3001
NODE_ENV=development

# M-Pesa API Configuration (Safaricom Daraja)
MPESA_ENVIRONMENT=sandbox
MPESA_CONSUMER_KEY=q2ZRFwyhJPeQysSvFAf60yWB6jgrGscMjSOSIg4KRvxGwFW3
MPESA_CONSUMER_SECRET=zRxo2AHpF8G8KXfosVL24Kwryfw4iBScvDHEgcyWQLEufgp3fn5LkbSJJYTMqAlR
MPESA_BUSINESS_SHORT_CODE=174379
MPESA_PASSKEY=your_passkey_here
MPESA_INITIATOR_NAME=testapi
MPESA_SECURITY_CREDENTIAL=zRxo2AHpF8G8KXfosVL24Kwryfw4iBScvDHEgcyWQLEufgp3fn5LkbSJJYTMqAlR

# M-Pesa API URLs
MPESA_BASE_URL=https://sandbox.safaricom.co.ke
MPESA_AUTH_URL=https://sandbox.safaricom.co.ke/oauth/v1/generate?grant_type=client_credentials
MPESA_STK_PUSH_URL=https://sandbox.safaricom.co.ke/mpesa/stkpush/v1/processrequest
MPESA_STK_QUERY_URL=https://sandbox.safaricom.co.ke/mpesa/stkpushquery/v1/query

# Database Configuration
DATABASE_URL=./data/mpesa_transactions.db
DATABASE_TYPE=sqlite

# Blockchain Configuration (from frontend)
BLOCKCHAIN_RPC_URL=https://data-seed-prebsc-1-s1.binance.org:8545/
BLOCKCHAIN_CHAIN_ID=97
USDT_CONTRACT_ADDRESS=******************************************
PACKAGE_MANAGER_CONTRACT_ADDRESS=your_package_manager_address
TREASURY_WALLET_ADDRESS=your_treasury_wallet_address
TREASURY_PRIVATE_KEY=your_treasury_private_key

# Security
JWT_SECRET=your_jwt_secret_here
API_RATE_LIMIT=100
CORS_ORIGIN=http://localhost:5173

# Callback URLs
CALLBACK_BASE_URL=https://your-ngrok-url.ngrok.io
MPESA_CALLBACK_URL=${CALLBACK_BASE_URL}/api/mpesa/callback
MPESA_TIMEOUT_URL=${CALLBACK_BASE_URL}/api/mpesa/timeout

# Logging
LOG_LEVEL=info
LOG_FILE=./logs/app.log

# Exchange Rate (KES to USD)
KES_TO_USD_RATE=0.0067
USD_TO_KES_RATE=149.25
