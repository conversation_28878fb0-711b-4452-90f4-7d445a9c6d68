import express from 'express';
import bridgeController from '../controllers/bridgeController.js';
import {
  createRateLimit,
  validateApi<PERSON>ey,
  logRequest
} from '../middleware/auth.js';

const router = express.Router();

// Apply rate limiting
router.use(createRateLimit(15 * 60 * 1000, 30)); // 30 requests per 15 minutes

// Apply request logging
router.use(logRequest);

// Process completed M-Pesa payments (can be called by cron job or webhook)
router.post('/process-payments', validateApiKey, bridgeController.processCompletedPayments);

// Complete purchase after M-Pesa payment
router.post('/complete-purchase', bridgeController.completePurchase);

// Get bridge status for a transaction
router.get('/status/:transactionId', bridgeController.getBridgeStatus);

// Manual retry for failed transactions
router.post('/retry/:transactionId', validateApiKey, bridgeController.retryTransaction);

export default router;
