import express from 'express';
import mpesaController from '../controllers/mpesaController.js';
import {
  createRateLimit,
  validateWalletAddress,
  validatePhoneNumber,
  validateAmount,
  validatePackageId,
  logRequest
} from '../middleware/auth.js';
import {
  verifyMpesaCallback,
  createOperationRateLimit,
  validateTransactionIntegrity,
  fraudDetection,
  sanitizeInput
} from '../middleware/security.js';

const router = express.Router();

// Apply input sanitization to all routes
router.use(sanitizeInput);

// Apply request logging
router.use(logRequest);

// Apply rate limiting to all M-Pesa routes
router.use(createRateLimit(15 * 60 * 1000, 50)); // 50 requests per 15 minutes

// Initiate M-Pesa payment
router.post('/initiate-payment', [
  createOperationRateLimit('payment-initiation', 60000, 3), // 3 attempts per minute
  fraudDetection,
  validateWalletAddress,
  validate<PERSON>hone<PERSON><PERSON>ber,
  validateAmount,
  validatePackageId
], mpesaController.initiatePayment);

// M-Pesa callback endpoint (with security verification)
router.post('/callback/:transactionId', [
  verifyMpesaCallback,
  validateTransactionIntegrity
], mpesaController.handleCallback);

// M-Pesa timeout endpoint
router.post('/timeout/:transactionId', [
  verifyMpesaCallback,
  validateTransactionIntegrity
], mpesaController.handleTimeout);

// Query payment status
router.get('/status/:checkoutRequestId', [
  createOperationRateLimit('status-query', 30000, 10) // 10 queries per 30 seconds
], mpesaController.queryPaymentStatus);

// Get transaction history for a wallet
router.get('/transactions/:walletAddress', [
  createOperationRateLimit('transaction-history', 60000, 5) // 5 requests per minute
], mpesaController.getTransactionHistory);

export default router;
