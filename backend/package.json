{"name": "blockcoop-mpesa-backend", "version": "1.0.0", "description": "Backend API for M-Pesa payment integration with BlockCoop Sacco", "main": "src/server.js", "type": "module", "scripts": {"start": "node src/simpleServer.js", "dev": "nodemon src/server.js", "test": "node scripts/runTests.js", "test:unit": "node scripts/runTests.js unit", "test:integration": "node scripts/runTests.js integration", "test:e2e": "node scripts/runTests.js e2e", "test:performance": "node scripts/runTests.js performance", "test:security": "node scripts/runTests.js security", "test:sandbox": "node scripts/runTests.js sandbox", "test:coverage": "node scripts/runTests.js coverage", "test:ci": "node scripts/runTests.js ci", "test:watch": "jest --watch", "db:migrate": "node src/database/migrate.js", "db:seed": "node src/database/seed.js"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "dotenv": "^16.3.1", "axios": "^1.6.0", "uuid": "^9.0.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "sqlite3": "^5.1.6", "ethers": "^6.8.1", "winston": "^3.11.0", "morgan": "^1.10.0", "compression": "^1.7.4"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "@types/jest": "^29.5.8"}, "keywords": ["mpesa", "blockchain", "usdt", "payment", "api", "nodejs", "express"], "author": "BlockCoop Sacco", "license": "MIT"}